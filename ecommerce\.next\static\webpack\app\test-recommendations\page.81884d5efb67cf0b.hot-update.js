"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-recommendations/page",{

/***/ "(app-pages-browser)/./app/test-recommendations/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-recommendations/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/product/RecentlyViewedProducts */ \"(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\");\n/* harmony import */ var _components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/product/ProductRecommendations */ \"(app-pages-browser)/./components/product/ProductRecommendations.tsx\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst TestRecommendationsPage = ()=>{\n    _s();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleRecommendationsUpdated = ()=>{\n        // Force re-render of recommendation components\n        setRefreshKey((prev)=>prev + 1);\n    };\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Test Recommendations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Please log in to test the recommendations feature.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-8\",\n                    children: \"Test Recommendations & Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Recently Viewed Products\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_3__.RecentlyViewedProducts, {\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"All Recommendations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"all\",\n                            limit: 8,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Category Based\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"category_based\",\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Brand Based\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"brand_based\",\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Trending Products\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"trending\",\n                            limit: 6,\n                            showTitle: false,\n                            layout: \"carousel\",\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Frequently Bought Together\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"frequently_bought\",\n                            limit: 4,\n                            showTitle: false,\n                            layout: \"list\",\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 p-6 bg-blue-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"How to Test:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Visit some product pages to add them to recently viewed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Add products to cart to track interactions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Make some purchases to improve recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Refresh this page to see updated recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Use the browser's developer tools to see API calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TestRecommendationsPage, \"Iq/LT6p/NB2Ua4L8jlQpzdOgQUI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = TestRecommendationsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestRecommendationsPage);\nvar _c;\n$RefreshReg$(_c, \"TestRecommendationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-recommendations/page.tsx\n"));

/***/ })

});