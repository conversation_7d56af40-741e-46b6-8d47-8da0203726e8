"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RecentlyViewedProducts = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_5__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            const fetchRecentlyViewed = {\n                \"RecentlyViewedProducts.useEffect.fetchRecentlyViewed\": async ()=>{\n                    // Only fetch when status is determined (not 'loading')\n                    if (status === 'authenticated') {\n                        setLoading(true);\n                        try {\n                            const response = await read('/api/v1/users/recently-viewed/');\n                            const data = Array.isArray(response) ? response : [];\n                            setRecentlyViewed(data);\n                        } catch (error) {\n                            console.error('Error fetching recently viewed:', error);\n                            setRecentlyViewed([]);\n                        } finally{\n                            setLoading(false);\n                        }\n                    } else if (status === 'unauthenticated') {\n                        // Only clear data when explicitly unauthenticated\n                        setRecentlyViewed([]);\n                        setLoading(false);\n                    }\n                // Do nothing when status is 'loading' to prevent unnecessary calls\n                }\n            }[\"RecentlyViewedProducts.useEffect.fetchRecentlyViewed\"];\n            fetchRecentlyViewed();\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], [\n        status\n    ]); // Removed 'read' from dependencies to prevent unnecessary re-renders\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    // Transform products to match ProductType interface\n    const transformedProducts = displayProducts.map((product)=>{\n        var _product_images_, _product_images, _product_category, _product_brand;\n        return {\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            rating: 0,\n            image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n            category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n            brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n            slug: product.slug\n        };\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: transformedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProducts, \"mTUmrS71Lw6UdhvLZSSaqXuEnuY=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProducts;\nvar _c;\n$RefreshReg$(_c, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcHJvZHVjdC9SZWNlbnRseVZpZXdlZFByb2R1Y3RzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUF3RTtBQUMzQjtBQUNMO0FBRUE7QUFDTztBQVF4QyxNQUFNTyx5QkFBZ0U7UUFBQyxFQUM1RUMsUUFBUSxDQUFDLEVBQ1RDLFlBQVksSUFBSSxFQUNoQkMsWUFBWSxFQUFFLEVBQ2Y7O0lBQ0MsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHViwrQ0FBUUEsQ0FBUSxFQUFFO0lBQzlELE1BQU0sQ0FBQ1csU0FBU0MsV0FBVyxHQUFHWiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLEVBQUVhLElBQUksRUFBRSxHQUFHVix5REFBTUEsQ0FBQ0Msb0RBQVFBLElBQUk7SUFDcEMsTUFBTSxFQUFFVSxNQUFNLEVBQUUsR0FBR2IsMkRBQVVBO0lBRTdCRixnREFBU0E7NENBQUM7WUFDUixNQUFNZ0I7d0VBQXNCO29CQUMxQix1REFBdUQ7b0JBQ3ZELElBQUlELFdBQVcsaUJBQWlCO3dCQUM5QkYsV0FBVzt3QkFDWCxJQUFJOzRCQUNGLE1BQU1JLFdBQVcsTUFBTUgsS0FBSzs0QkFDNUIsTUFBTUksT0FBT0MsTUFBTUMsT0FBTyxDQUFDSCxZQUFZQSxXQUFXLEVBQUU7NEJBQ3BETixrQkFBa0JPO3dCQUNwQixFQUFFLE9BQU9HLE9BQU87NEJBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBOzRCQUNqRFYsa0JBQWtCLEVBQUU7d0JBQ3RCLFNBQVU7NEJBQ1JFLFdBQVc7d0JBQ2I7b0JBQ0YsT0FBTyxJQUFJRSxXQUFXLG1CQUFtQjt3QkFDdkMsa0RBQWtEO3dCQUNsREosa0JBQWtCLEVBQUU7d0JBQ3BCRSxXQUFXO29CQUNiO2dCQUNBLG1FQUFtRTtnQkFDckU7O1lBRUFHO1FBQ0Y7MkNBQUc7UUFBQ0Q7S0FBTyxHQUFHLHFFQUFxRTtJQUVuRixJQUFJQSxXQUFXLG1CQUFvQixDQUFDSCxXQUFXLEVBQUNGLDJCQUFBQSxxQ0FBQUEsZUFBZ0JhLE1BQU0sR0FBRztRQUN2RSxPQUFPO0lBQ1Q7SUFFQSxNQUFNQyxrQkFBa0JkLGVBQWVlLEtBQUssQ0FBQyxHQUFHbEI7SUFFaEQsb0RBQW9EO0lBQ3BELE1BQU1tQixzQkFBcUNGLGdCQUFnQkcsR0FBRyxDQUFDQyxDQUFBQTtZQUt0REEsa0JBQUFBLGlCQUNHQSxtQkFDSEE7ZUFQa0U7WUFDekVDLElBQUlELFFBQVFDLEVBQUU7WUFDZEMsTUFBTUYsUUFBUUUsSUFBSTtZQUNsQkMsT0FBT0gsUUFBUUcsS0FBSztZQUNwQkMsUUFBUTtZQUNSQyxPQUFPTCxFQUFBQSxrQkFBQUEsUUFBUU0sTUFBTSxjQUFkTix1Q0FBQUEsbUJBQUFBLGVBQWdCLENBQUMsRUFBRSxjQUFuQkEsdUNBQUFBLGlCQUFxQkssS0FBSyxLQUFJO1lBQ3JDRSxVQUFVUCxFQUFBQSxvQkFBQUEsUUFBUU8sUUFBUSxjQUFoQlAsd0NBQUFBLGtCQUFrQkUsSUFBSSxLQUFJO1lBQ3BDTSxPQUFPUixFQUFBQSxpQkFBQUEsUUFBUVEsS0FBSyxjQUFiUixxQ0FBQUEsZUFBZUUsSUFBSSxLQUFJO1lBQzlCTyxNQUFNVCxRQUFRUyxJQUFJO1FBQ3BCOztJQUVBLElBQUl6QixTQUFTO1FBQ1gscUJBQ0UsOERBQUMwQjtZQUFJN0IsV0FBVyw0QkFBc0MsT0FBVkE7O2dCQUN6Q0QsMkJBQ0MsOERBQUMrQjtvQkFBRzlCLFdBQVU7OEJBQTZCOzs7Ozs7OEJBRTdDLDhEQUFDNkI7b0JBQUk3QixXQUFVOzhCQUNaVSxNQUFNcUIsSUFBSSxDQUFDO3dCQUFFakIsUUFBUWhCO29CQUFNLEdBQUdvQixHQUFHLENBQUMsQ0FBQ2MsR0FBR0Msc0JBQ3JDLDhEQUFDSjs0QkFBZ0I3QixXQUFVOzs4Q0FDekIsOERBQUM2QjtvQ0FBSTdCLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQzZCO29DQUFJN0IsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDNkI7b0NBQUk3QixXQUFVOzs7Ozs7OzJCQUhQaUM7Ozs7Ozs7Ozs7Ozs7Ozs7SUFTcEI7SUFFQSxxQkFDRSw4REFBQ0o7UUFBSTdCLFdBQVcsNEJBQXNDLE9BQVZBOztZQUN6Q0QsMkJBQ0MsOERBQUMrQjtnQkFBRzlCLFdBQVU7MEJBQTZCOzs7Ozs7MEJBRTdDLDhEQUFDNkI7Z0JBQUk3QixXQUFVOzBCQUNaaUIsb0JBQW9CQyxHQUFHLENBQUMsQ0FBQ0Msd0JBQ3hCLDhEQUFDekIsb0RBQVdBO3dCQUFtQixHQUFHeUIsT0FBTzt1QkFBdkJBLFFBQVFDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLdEMsRUFBRTtHQXJGV3ZCOztRQU9NRixxREFBTUE7UUFDSkYsdURBQVVBOzs7S0FSbEJJIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxjb21wb25lbnRzXFxwcm9kdWN0XFxSZWNlbnRseVZpZXdlZFByb2R1Y3RzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuaW1wb3J0IFByb2R1Y3RDYXJkIGZyb20gJy4vUHJvZHVjdENhcmQnO1xuaW1wb3J0IHsgUHJvZHVjdFR5cGUgfSBmcm9tICcuLi8uLi90eXBlcy9wcm9kdWN0LmQnO1xuaW1wb3J0IHVzZUFwaSBmcm9tICcuLi8uLi9ob29rcy91c2VBcGknO1xuaW1wb3J0IHsgTUFJTl9VUkwgfSBmcm9tICcuLi8uLi9jb25zdGFudC91cmxzJztcblxuaW50ZXJmYWNlIFJlY2VudGx5Vmlld2VkUHJvZHVjdHNQcm9wcyB7XG4gIGxpbWl0PzogbnVtYmVyO1xuICBzaG93VGl0bGU/OiBib29sZWFuO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBSZWNlbnRseVZpZXdlZFByb2R1Y3RzOiBSZWFjdC5GQzxSZWNlbnRseVZpZXdlZFByb2R1Y3RzUHJvcHM+ID0gKHtcbiAgbGltaXQgPSA1LFxuICBzaG93VGl0bGUgPSB0cnVlLFxuICBjbGFzc05hbWUgPSBcIlwiXG59KSA9PiB7XG4gIGNvbnN0IFtyZWNlbnRseVZpZXdlZCwgc2V0UmVjZW50bHlWaWV3ZWRdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IHsgcmVhZCB9ID0gdXNlQXBpKE1BSU5fVVJMIHx8ICcnKTtcbiAgY29uc3QgeyBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGZldGNoUmVjZW50bHlWaWV3ZWQgPSBhc3luYyAoKSA9PiB7XG4gICAgICAvLyBPbmx5IGZldGNoIHdoZW4gc3RhdHVzIGlzIGRldGVybWluZWQgKG5vdCAnbG9hZGluZycpXG4gICAgICBpZiAoc3RhdHVzID09PSAnYXV0aGVudGljYXRlZCcpIHtcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHJlYWQoJy9hcGkvdjEvdXNlcnMvcmVjZW50bHktdmlld2VkLycpO1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBBcnJheS5pc0FycmF5KHJlc3BvbnNlKSA/IHJlc3BvbnNlIDogW107XG4gICAgICAgICAgc2V0UmVjZW50bHlWaWV3ZWQoZGF0YSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcmVjZW50bHkgdmlld2VkOicsIGVycm9yKTtcbiAgICAgICAgICBzZXRSZWNlbnRseVZpZXdlZChbXSk7XG4gICAgICAgIH0gZmluYWxseSB7XG4gICAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSAndW5hdXRoZW50aWNhdGVkJykge1xuICAgICAgICAvLyBPbmx5IGNsZWFyIGRhdGEgd2hlbiBleHBsaWNpdGx5IHVuYXV0aGVudGljYXRlZFxuICAgICAgICBzZXRSZWNlbnRseVZpZXdlZChbXSk7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgICAgLy8gRG8gbm90aGluZyB3aGVuIHN0YXR1cyBpcyAnbG9hZGluZycgdG8gcHJldmVudCB1bm5lY2Vzc2FyeSBjYWxsc1xuICAgIH07XG5cbiAgICBmZXRjaFJlY2VudGx5Vmlld2VkKCk7XG4gIH0sIFtzdGF0dXNdKTsgLy8gUmVtb3ZlZCAncmVhZCcgZnJvbSBkZXBlbmRlbmNpZXMgdG8gcHJldmVudCB1bm5lY2Vzc2FyeSByZS1yZW5kZXJzXG5cbiAgaWYgKHN0YXR1cyAhPT0gJ2F1dGhlbnRpY2F0ZWQnIHx8ICghbG9hZGluZyAmJiAhcmVjZW50bHlWaWV3ZWQ/Lmxlbmd0aCkpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIGNvbnN0IGRpc3BsYXlQcm9kdWN0cyA9IHJlY2VudGx5Vmlld2VkLnNsaWNlKDAsIGxpbWl0KTtcblxuICAvLyBUcmFuc2Zvcm0gcHJvZHVjdHMgdG8gbWF0Y2ggUHJvZHVjdFR5cGUgaW50ZXJmYWNlXG4gIGNvbnN0IHRyYW5zZm9ybWVkUHJvZHVjdHM6IFByb2R1Y3RUeXBlW10gPSBkaXNwbGF5UHJvZHVjdHMubWFwKHByb2R1Y3QgPT4gKHtcbiAgICBpZDogcHJvZHVjdC5pZCxcbiAgICBuYW1lOiBwcm9kdWN0Lm5hbWUsXG4gICAgcHJpY2U6IHByb2R1Y3QucHJpY2UsXG4gICAgcmF0aW5nOiAwLCAvLyBEZWZhdWx0IHJhdGluZyBzaW5jZSBpdCdzIG5vdCBpbiBvdXIgQVBJIHJlc3BvbnNlXG4gICAgaW1hZ2U6IHByb2R1Y3QuaW1hZ2VzPy5bMF0/LmltYWdlIHx8ICcvcGxhY2Vob2xkZXIuanBnJyxcbiAgICBjYXRlZ29yeTogcHJvZHVjdC5jYXRlZ29yeT8ubmFtZSB8fCAnJyxcbiAgICBicmFuZDogcHJvZHVjdC5icmFuZD8ubmFtZSB8fCAnJyxcbiAgICBzbHVnOiBwcm9kdWN0LnNsdWdcbiAgfSkpO1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcmVjZW50bHktdmlld2VkLXByb2R1Y3RzICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgICB7c2hvd1RpdGxlICYmIChcbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5SZWNlbnRseSBWaWV3ZWQ8L2gzPlxuICAgICAgICApfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTMgbGc6Z3JpZC1jb2xzLTUgZ2FwLTRcIj5cbiAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogbGltaXQgfSkubWFwKChfLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS0zMDAgaC00OCByb3VuZGVkLWxnIG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTMwMCBoLTQgcm91bmRlZCBtYi0xXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS0zMDAgaC00IHJvdW5kZWQgdy0zLzRcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcmVjZW50bHktdmlld2VkLXByb2R1Y3RzICR7Y2xhc3NOYW1lfWB9PlxuICAgICAge3Nob3dUaXRsZSAmJiAoXG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlJlY2VudGx5IFZpZXdlZDwvaDM+XG4gICAgICApfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy0zIGxnOmdyaWQtY29scy01IGdhcC00XCI+XG4gICAgICAgIHt0cmFuc2Zvcm1lZFByb2R1Y3RzLm1hcCgocHJvZHVjdCkgPT4gKFxuICAgICAgICAgIDxQcm9kdWN0Q2FyZCBrZXk9e3Byb2R1Y3QuaWR9IHsuLi5wcm9kdWN0fSAvPlxuICAgICAgICApKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlU2Vzc2lvbiIsIlByb2R1Y3RDYXJkIiwidXNlQXBpIiwiTUFJTl9VUkwiLCJSZWNlbnRseVZpZXdlZFByb2R1Y3RzIiwibGltaXQiLCJzaG93VGl0bGUiLCJjbGFzc05hbWUiLCJyZWNlbnRseVZpZXdlZCIsInNldFJlY2VudGx5Vmlld2VkIiwibG9hZGluZyIsInNldExvYWRpbmciLCJyZWFkIiwic3RhdHVzIiwiZmV0Y2hSZWNlbnRseVZpZXdlZCIsInJlc3BvbnNlIiwiZGF0YSIsIkFycmF5IiwiaXNBcnJheSIsImVycm9yIiwiY29uc29sZSIsImxlbmd0aCIsImRpc3BsYXlQcm9kdWN0cyIsInNsaWNlIiwidHJhbnNmb3JtZWRQcm9kdWN0cyIsIm1hcCIsInByb2R1Y3QiLCJpZCIsIm5hbWUiLCJwcmljZSIsInJhdGluZyIsImltYWdlIiwiaW1hZ2VzIiwiY2F0ZWdvcnkiLCJicmFuZCIsInNsdWciLCJkaXYiLCJoMyIsImZyb20iLCJfIiwiaW5kZXgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});