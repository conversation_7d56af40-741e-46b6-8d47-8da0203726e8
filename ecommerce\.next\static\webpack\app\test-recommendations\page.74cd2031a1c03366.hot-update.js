"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-recommendations/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RecentlyViewedProducts = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_5__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            const fetchRecentlyViewed = {\n                \"RecentlyViewedProducts.useEffect.fetchRecentlyViewed\": async ()=>{\n                    if (status === 'authenticated') {\n                        setLoading(true);\n                        try {\n                            const response = await read('/api/v1/users/recently-viewed/');\n                            const data = Array.isArray(response) ? response : [];\n                            setRecentlyViewed(data);\n                        } catch (error) {\n                            console.error('Error fetching recently viewed:', error);\n                            setRecentlyViewed([]);\n                        } finally{\n                            setLoading(false);\n                        }\n                    } else {\n                        setRecentlyViewed([]);\n                        setLoading(false);\n                    }\n                }\n            }[\"RecentlyViewedProducts.useEffect.fetchRecentlyViewed\"];\n            fetchRecentlyViewed();\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], [\n        status,\n        read\n    ]);\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    // Transform products to match ProductType interface\n    const transformedProducts = displayProducts.map((product)=>{\n        var _product_images_, _product_images, _product_category, _product_brand;\n        return {\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            rating: 0,\n            image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n            category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n            brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n            slug: product.slug\n        };\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: transformedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProducts, \"mTUmrS71Lw6UdhvLZSSaqXuEnuY=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProducts;\nvar _c;\n$RefreshReg$(_c, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});