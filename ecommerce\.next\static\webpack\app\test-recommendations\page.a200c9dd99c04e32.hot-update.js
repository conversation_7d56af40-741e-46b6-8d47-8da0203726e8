"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-recommendations/page",{

/***/ "(app-pages-browser)/./app/test-recommendations/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-recommendations/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/product/RecentlyViewedProducts */ \"(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\");\n/* harmony import */ var _components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/product/ProductRecommendations */ \"(app-pages-browser)/./components/product/ProductRecommendations.tsx\");\n/* harmony import */ var _components_product_RecommendationRefresher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/product/RecommendationRefresher */ \"(app-pages-browser)/./components/product/RecommendationRefresher.tsx\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst TestRecommendationsPage = ()=>{\n    _s();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleRecommendationsUpdated = ()=>{\n        // Force re-render of recommendation components\n        setRefreshKey((prev)=>prev + 1);\n    };\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Test Recommendations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Please log in to test the recommendations feature.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Test Recommendations & Recently Viewed\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecommendationRefresher__WEBPACK_IMPORTED_MODULE_5__.RecommendationRefresher, {\n                            onRecommendationsUpdated: handleRecommendationsUpdated,\n                            showButton: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Recently Viewed Products\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_3__.RecentlyViewedProducts, {\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"All Recommendations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"all\",\n                            limit: 8,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Category Based\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"category_based\",\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Brand Based\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"brand_based\",\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Trending Products\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"trending\",\n                            limit: 6,\n                            showTitle: false,\n                            layout: \"carousel\",\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Frequently Bought Together\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"frequently_bought\",\n                            limit: 4,\n                            showTitle: false,\n                            layout: \"list\",\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 p-6 bg-blue-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"How to Test:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Visit some product pages to add them to recently viewed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Add products to cart to track interactions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Make some purchases to improve recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Refresh this page to see updated recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Use the browser's developer tools to see API calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TestRecommendationsPage, \"Iq/LT6p/NB2Ua4L8jlQpzdOgQUI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = TestRecommendationsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestRecommendationsPage);\nvar _c;\n$RefreshReg$(_c, \"TestRecommendationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC90ZXN0LXJlY29tbWVuZGF0aW9ucy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDd0M7QUFDSztBQUM0QztBQUNBO0FBQ0U7QUFDaEQ7QUFFM0MsTUFBTU8sMEJBQTBCOztJQUM5QixNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHTiwyREFBVUE7SUFDN0IsTUFBTSxDQUFDTyxZQUFZQyxjQUFjLEdBQUdULCtDQUFRQSxDQUFDO0lBRTdDLE1BQU1VLCtCQUErQjtRQUNuQywrQ0FBK0M7UUFDL0NELGNBQWNFLENBQUFBLE9BQVFBLE9BQU87SUFDL0I7SUFFQSxJQUFJSixXQUFXLFdBQVc7UUFDeEIscUJBQ0UsOERBQUNGLHVEQUFPQTtzQkFDTiw0RUFBQ087Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUFjOzs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsSUFBSU4sV0FBVyxtQkFBbUI7UUFDaEMscUJBQ0UsOERBQUNGLHVEQUFPQTtzQkFDTiw0RUFBQ087Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQTBCOzs7Ozs7c0NBQ3hDLDhEQUFDRTtzQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtiO0lBRUEscUJBQ0UsOERBQUNWLHVEQUFPQTtrQkFDTiw0RUFBQ087WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXFCOzs7Ozs7c0NBQ25DLDhEQUFDVCxnR0FBdUJBOzRCQUN0QlksMEJBQTBCTjs0QkFDMUJPLFlBQVk7Ozs7Ozs7Ozs7Ozs4QkFLaEIsOERBQUNMO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQUdMLFdBQVU7c0NBQThCOzs7Ozs7c0NBQzVDLDhEQUFDWCw4RkFBc0JBOzRCQUNyQmlCLE9BQU87NEJBQ1BDLFdBQVc7NEJBQ1hQLFdBQVU7Ozs7Ozs7Ozs7Ozs4QkFLZCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFBR0wsV0FBVTtzQ0FBOEI7Ozs7OztzQ0FDNUMsOERBQUNWLDhGQUFzQkE7NEJBQ3JCa0IsTUFBSzs0QkFDTEYsT0FBTzs0QkFDUEMsV0FBVzs0QkFDWFAsV0FBVTs7Ozs7Ozs7Ozs7OzhCQUtkLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFHTCxXQUFVO3NDQUE4Qjs7Ozs7O3NDQUM1Qyw4REFBQ1YsOEZBQXNCQTs0QkFDckJrQixNQUFLOzRCQUNMRixPQUFPOzRCQUNQQyxXQUFXOzRCQUNYUCxXQUFVOzs7Ozs7Ozs7Ozs7OEJBS2QsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQUdMLFdBQVU7c0NBQThCOzs7Ozs7c0NBQzVDLDhEQUFDViw4RkFBc0JBOzRCQUNyQmtCLE1BQUs7NEJBQ0xGLE9BQU87NEJBQ1BDLFdBQVc7NEJBQ1hQLFdBQVU7Ozs7Ozs7Ozs7Ozs4QkFLZCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFBR0wsV0FBVTtzQ0FBOEI7Ozs7OztzQ0FDNUMsOERBQUNWLDhGQUFzQkE7NEJBQ3JCa0IsTUFBSzs0QkFDTEYsT0FBTzs0QkFDUEMsV0FBVzs0QkFDWEUsUUFBTzs0QkFDUFQsV0FBVTs7Ozs7Ozs7Ozs7OzhCQUtkLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFHTCxXQUFVO3NDQUE4Qjs7Ozs7O3NDQUM1Qyw4REFBQ1YsOEZBQXNCQTs0QkFDckJrQixNQUFLOzRCQUNMRixPQUFPOzRCQUNQQyxXQUFXOzRCQUNYRSxRQUFPOzRCQUNQVCxXQUFVOzs7Ozs7Ozs7Ozs7OEJBS2QsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1U7NEJBQUdWLFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzNDLDhEQUFDVzs0QkFBR1gsV0FBVTs7OENBQ1osOERBQUNZOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7OENBQ0osOERBQUNBOzhDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1oQjtHQTVITW5COztRQUNlTCx1REFBVUE7OztLQUR6Qks7QUE4SE4saUVBQWVBLHVCQUF1QkEsRUFBQyIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcYXBwXFx0ZXN0LXJlY29tbWVuZGF0aW9uc1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFJlY2VudGx5Vmlld2VkUHJvZHVjdHMgfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL3Byb2R1Y3QvUmVjZW50bHlWaWV3ZWRQcm9kdWN0cyc7XG5pbXBvcnQgeyBQcm9kdWN0UmVjb21tZW5kYXRpb25zIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9wcm9kdWN0L1Byb2R1Y3RSZWNvbW1lbmRhdGlvbnMnO1xuaW1wb3J0IHsgUmVjb21tZW5kYXRpb25SZWZyZXNoZXIgfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL3Byb2R1Y3QvUmVjb21tZW5kYXRpb25SZWZyZXNoZXInO1xuaW1wb3J0IE1haW5IT0YgZnJvbSAnLi4vLi4vbGF5b3V0L01haW5IT0YnO1xuXG5jb25zdCBUZXN0UmVjb21tZW5kYXRpb25zUGFnZSA9ICgpID0+IHtcbiAgY29uc3QgeyBzdGF0dXMgfSA9IHVzZVNlc3Npb24oKTtcbiAgY29uc3QgW3JlZnJlc2hLZXksIHNldFJlZnJlc2hLZXldID0gdXNlU3RhdGUoMCk7XG5cbiAgY29uc3QgaGFuZGxlUmVjb21tZW5kYXRpb25zVXBkYXRlZCA9ICgpID0+IHtcbiAgICAvLyBGb3JjZSByZS1yZW5kZXIgb2YgcmVjb21tZW5kYXRpb24gY29tcG9uZW50c1xuICAgIHNldFJlZnJlc2hLZXkocHJldiA9PiBwcmV2ICsgMSk7XG4gIH07XG5cbiAgaWYgKHN0YXR1cyA9PT0gJ2xvYWRpbmcnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxNYWluSE9GPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5Mb2FkaW5nLi4uPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9NYWluSE9GPlxuICAgICk7XG4gIH1cblxuICBpZiAoc3RhdHVzID09PSAndW5hdXRoZW50aWNhdGVkJykge1xuICAgIHJldHVybiAoXG4gICAgICA8TWFpbkhPRj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj5UZXN0IFJlY29tbWVuZGF0aW9uczwvaDE+XG4gICAgICAgICAgICA8cD5QbGVhc2UgbG9nIGluIHRvIHRlc3QgdGhlIHJlY29tbWVuZGF0aW9ucyBmZWF0dXJlLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L01haW5IT0Y+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPE1haW5IT0Y+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi04XCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZFwiPlRlc3QgUmVjb21tZW5kYXRpb25zICYgUmVjZW50bHkgVmlld2VkPC9oMT5cbiAgICAgICAgICA8UmVjb21tZW5kYXRpb25SZWZyZXNoZXJcbiAgICAgICAgICAgIG9uUmVjb21tZW5kYXRpb25zVXBkYXRlZD17aGFuZGxlUmVjb21tZW5kYXRpb25zVXBkYXRlZH1cbiAgICAgICAgICAgIHNob3dCdXR0b249e3RydWV9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogUmVjZW50bHkgVmlld2VkIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMTJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBtYi00XCI+UmVjZW50bHkgVmlld2VkIFByb2R1Y3RzPC9oMj5cbiAgICAgICAgICA8UmVjZW50bHlWaWV3ZWRQcm9kdWN0c1xuICAgICAgICAgICAgbGltaXQ9ezZ9XG4gICAgICAgICAgICBzaG93VGl0bGU9e2ZhbHNlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC00XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQWxsIFJlY29tbWVuZGF0aW9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0xMlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTRcIj5BbGwgUmVjb21tZW5kYXRpb25zPC9oMj5cbiAgICAgICAgICA8UHJvZHVjdFJlY29tbWVuZGF0aW9uc1xuICAgICAgICAgICAgdHlwZT1cImFsbFwiXG4gICAgICAgICAgICBsaW1pdD17OH1cbiAgICAgICAgICAgIHNob3dUaXRsZT17ZmFsc2V9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZC1sZyBwLTRcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDYXRlZ29yeSBCYXNlZCBSZWNvbW1lbmRhdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMTJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBtYi00XCI+Q2F0ZWdvcnkgQmFzZWQ8L2gyPlxuICAgICAgICAgIDxQcm9kdWN0UmVjb21tZW5kYXRpb25zXG4gICAgICAgICAgICB0eXBlPVwiY2F0ZWdvcnlfYmFzZWRcIlxuICAgICAgICAgICAgbGltaXQ9ezZ9XG4gICAgICAgICAgICBzaG93VGl0bGU9e2ZhbHNlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC00XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQnJhbmQgQmFzZWQgUmVjb21tZW5kYXRpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTEyXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPkJyYW5kIEJhc2VkPC9oMj5cbiAgICAgICAgICA8UHJvZHVjdFJlY29tbWVuZGF0aW9uc1xuICAgICAgICAgICAgdHlwZT1cImJyYW5kX2Jhc2VkXCJcbiAgICAgICAgICAgIGxpbWl0PXs2fVxuICAgICAgICAgICAgc2hvd1RpdGxlPXtmYWxzZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNFwiXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFRyZW5kaW5nIFByb2R1Y3RzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTEyXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPlRyZW5kaW5nIFByb2R1Y3RzPC9oMj5cbiAgICAgICAgICA8UHJvZHVjdFJlY29tbWVuZGF0aW9uc1xuICAgICAgICAgICAgdHlwZT1cInRyZW5kaW5nXCJcbiAgICAgICAgICAgIGxpbWl0PXs2fVxuICAgICAgICAgICAgc2hvd1RpdGxlPXtmYWxzZX1cbiAgICAgICAgICAgIGxheW91dD1cImNhcm91c2VsXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNFwiXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZyZXF1ZW50bHkgQm91Z2h0IFRvZ2V0aGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTEyXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPkZyZXF1ZW50bHkgQm91Z2h0IFRvZ2V0aGVyPC9oMj5cbiAgICAgICAgICA8UHJvZHVjdFJlY29tbWVuZGF0aW9uc1xuICAgICAgICAgICAgdHlwZT1cImZyZXF1ZW50bHlfYm91Z2h0XCJcbiAgICAgICAgICAgIGxpbWl0PXs0fVxuICAgICAgICAgICAgc2hvd1RpdGxlPXtmYWxzZX1cbiAgICAgICAgICAgIGxheW91dD1cImxpc3RcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC00XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogSW5zdHJ1Y3Rpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTE2IHAtNiBiZy1ibHVlLTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTJcIj5Ib3cgdG8gVGVzdDo8L2gzPlxuICAgICAgICAgIDxvbCBjbGFzc05hbWU9XCJsaXN0LWRlY2ltYWwgbGlzdC1pbnNpZGUgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICA8bGk+VmlzaXQgc29tZSBwcm9kdWN0IHBhZ2VzIHRvIGFkZCB0aGVtIHRvIHJlY2VudGx5IHZpZXdlZDwvbGk+XG4gICAgICAgICAgICA8bGk+QWRkIHByb2R1Y3RzIHRvIGNhcnQgdG8gdHJhY2sgaW50ZXJhY3Rpb25zPC9saT5cbiAgICAgICAgICAgIDxsaT5NYWtlIHNvbWUgcHVyY2hhc2VzIHRvIGltcHJvdmUgcmVjb21tZW5kYXRpb25zPC9saT5cbiAgICAgICAgICAgIDxsaT5SZWZyZXNoIHRoaXMgcGFnZSB0byBzZWUgdXBkYXRlZCByZWNvbW1lbmRhdGlvbnM8L2xpPlxuICAgICAgICAgICAgPGxpPlVzZSB0aGUgYnJvd3NlcidzIGRldmVsb3BlciB0b29scyB0byBzZWUgQVBJIGNhbGxzPC9saT5cbiAgICAgICAgICA8L29sPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvTWFpbkhPRj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFRlc3RSZWNvbW1lbmRhdGlvbnNQYWdlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VTZXNzaW9uIiwiUmVjZW50bHlWaWV3ZWRQcm9kdWN0cyIsIlByb2R1Y3RSZWNvbW1lbmRhdGlvbnMiLCJSZWNvbW1lbmRhdGlvblJlZnJlc2hlciIsIk1haW5IT0YiLCJUZXN0UmVjb21tZW5kYXRpb25zUGFnZSIsInN0YXR1cyIsInJlZnJlc2hLZXkiLCJzZXRSZWZyZXNoS2V5IiwiaGFuZGxlUmVjb21tZW5kYXRpb25zVXBkYXRlZCIsInByZXYiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJvblJlY29tbWVuZGF0aW9uc1VwZGF0ZWQiLCJzaG93QnV0dG9uIiwiaDIiLCJsaW1pdCIsInNob3dUaXRsZSIsInR5cGUiLCJsYXlvdXQiLCJoMyIsIm9sIiwibGkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-recommendations/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/product/RecommendationRefresher.tsx":
/*!********************************************************!*\
  !*** ./components/product/RecommendationRefresher.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationRefresher: () => (/* binding */ RecommendationRefresher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecommendations */ \"(app-pages-browser)/./hooks/useRecommendations.ts\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_RefreshCw_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=RefreshCw,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RecommendationRefresher = (param)=>{\n    let { onRecommendationsUpdated, showButton = true, autoRefresh = false, className = \"\" } = param;\n    _s();\n    const { generateRecommendations } = (0,_hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations)();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastGenerated, setLastGenerated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleGenerateRecommendations = async ()=>{\n        if (status !== 'authenticated' || isGenerating) return;\n        setIsGenerating(true);\n        try {\n            await generateRecommendations();\n            setLastGenerated(new Date());\n            onRecommendationsUpdated === null || onRecommendationsUpdated === void 0 ? void 0 : onRecommendationsUpdated();\n        } catch (error) {\n            console.error('Error generating recommendations:', error);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    // Auto-refresh recommendations when component mounts (if enabled)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecommendationRefresher.useEffect\": ()=>{\n            if (autoRefresh && status === 'authenticated' && !lastGenerated) {\n                handleGenerateRecommendations();\n            }\n        }\n    }[\"RecommendationRefresher.useEffect\"], [\n        autoRefresh,\n        status\n    ]);\n    if (status !== 'authenticated') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recommendation-refresher \".concat(className),\n        children: [\n            showButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: handleGenerateRecommendations,\n                disabled: isGenerating,\n                variant: \"outline\",\n                size: \"sm\",\n                className: \"flex items-center gap-2\",\n                children: [\n                    isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecommendationRefresher.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RefreshCw_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecommendationRefresher.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, undefined),\n                    isGenerating ? 'Generating...' : 'Refresh Recommendations'\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecommendationRefresher.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, undefined),\n            lastGenerated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xs text-gray-500 mt-2\",\n                children: [\n                    \"Last updated: \",\n                    lastGenerated.toLocaleTimeString()\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecommendationRefresher.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecommendationRefresher.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecommendationRefresher, \"LETCekpwlkijUkASrsRrTrol4fM=\", false, function() {\n    return [\n        _hooks_useRecommendations__WEBPACK_IMPORTED_MODULE_3__.useRecommendations,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecommendationRefresher;\nvar _c;\n$RefreshReg$(_c, \"RecommendationRefresher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecommendationRefresher.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RefreshCw)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RefreshCw\", [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n]);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Zap\", [\n    [\n        \"path\",\n        {\n            d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n            key: \"1xq2db\"\n        }\n    ]\n]);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ })

});