"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RecentlyViewedProducts = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_5__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Use refs to track request state and prevent multiple calls\n    const isRequestInProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const hasDataBeenFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastFetchedStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    const fetchRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RecentlyViewedProducts.useCallback[fetchRecentlyViewed]\": async ()=>{\n            // Prevent multiple simultaneous requests\n            if (isRequestInProgress.current) {\n                console.log('Request already in progress, skipping...');\n                return;\n            }\n            // Skip if we already have data for this authentication status\n            if (hasDataBeenFetched.current && lastFetchedStatus.current === status) {\n                console.log('Data already fetched for this status, skipping...');\n                return;\n            }\n            // Only fetch when status is determined (not 'loading')\n            if (status === 'authenticated') {\n                isRequestInProgress.current = true;\n                setLoading(true);\n                try {\n                    console.log('Fetching recently viewed products...');\n                    const response = await read('/api/v1/users/recently-viewed/');\n                    const data = Array.isArray(response) ? response : [];\n                    setRecentlyViewed(data);\n                    hasDataBeenFetched.current = true;\n                    lastFetchedStatus.current = status;\n                } catch (error) {\n                    console.error('Error fetching recently viewed:', error);\n                    setRecentlyViewed([]);\n                } finally{\n                    setLoading(false);\n                    isRequestInProgress.current = false;\n                }\n            } else if (status === 'unauthenticated') {\n                // Only clear data when explicitly unauthenticated\n                setRecentlyViewed([]);\n                setLoading(false);\n                hasDataBeenFetched.current = true;\n                lastFetchedStatus.current = status;\n            }\n        // Do nothing when status is 'loading' to prevent unnecessary calls\n        }\n    }[\"RecentlyViewedProducts.useCallback[fetchRecentlyViewed]\"], [\n        status,\n        read\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            fetchRecentlyViewed();\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], [\n        fetchRecentlyViewed\n    ]);\n    // Reset fetch state when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProducts.useEffect\": ()=>{\n            return ({\n                \"RecentlyViewedProducts.useEffect\": ()=>{\n                    isRequestInProgress.current = false;\n                    hasDataBeenFetched.current = false;\n                    lastFetchedStatus.current = '';\n                }\n            })[\"RecentlyViewedProducts.useEffect\"];\n        }\n    }[\"RecentlyViewedProducts.useEffect\"], []);\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    // Transform products to match ProductType interface\n    const transformedProducts = displayProducts.map((product)=>{\n        var _product_images_, _product_images, _product_category, _product_brand;\n        return {\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            rating: 0,\n            image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n            category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n            brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n            slug: product.slug\n        };\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: transformedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProducts, \"ReObhpFpm4KfwjZjMiv39tbGGl0=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProducts;\nvar _c;\n$RefreshReg$(_c, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});