"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-recommendations/page",{

/***/ "(app-pages-browser)/./app/test-recommendations/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-recommendations/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/product/RecentlyViewedProducts */ \"(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\");\n/* harmony import */ var _components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/product/ProductRecommendations */ \"(app-pages-browser)/./components/product/ProductRecommendations.tsx\");\n/* harmony import */ var _components_product_RecommendationRefresher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/product/RecommendationRefresher */ \"(app-pages-browser)/./components/product/RecommendationRefresher.tsx\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst TestRecommendationsPage = ()=>{\n    _s();\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleRecommendationsUpdated = ()=>{\n        // Force re-render of recommendation components\n        setRefreshKey((prev)=>prev + 1);\n    };\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Test Recommendations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Please log in to test the recommendations feature.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"Test Recommendations & Recently Viewed\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecommendationRefresher__WEBPACK_IMPORTED_MODULE_5__.RecommendationRefresher, {\n                            onRecommendationsUpdated: handleRecommendationsUpdated,\n                            showButton: true\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Recently Viewed Products\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_RecentlyViewedProducts__WEBPACK_IMPORTED_MODULE_3__.RecentlyViewedProducts, {\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"All Recommendations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"all\",\n                            limit: 8,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, \"all-\".concat(refreshKey), false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Category Based\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"category_based\",\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Brand Based\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"brand_based\",\n                            limit: 6,\n                            showTitle: false,\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Trending Products\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"trending\",\n                            limit: 6,\n                            showTitle: false,\n                            layout: \"carousel\",\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Frequently Bought Together\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductRecommendations__WEBPACK_IMPORTED_MODULE_4__.ProductRecommendations, {\n                            type: \"frequently_bought\",\n                            limit: 4,\n                            showTitle: false,\n                            layout: \"list\",\n                            className: \"border rounded-lg p-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 p-6 bg-blue-50 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"How to Test:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Visit some product pages to add them to recently viewed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Add products to cart to track interactions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Make some purchases to improve recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Refresh this page to see updated recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Use the browser's developer tools to see API calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\test-recommendations\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TestRecommendationsPage, \"Iq/LT6p/NB2Ua4L8jlQpzdOgQUI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = TestRecommendationsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestRecommendationsPage);\nvar _c;\n$RefreshReg$(_c, \"TestRecommendationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-recommendations/page.tsx\n"));

/***/ })

});