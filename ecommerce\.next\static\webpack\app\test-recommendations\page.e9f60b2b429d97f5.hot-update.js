"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-recommendations/page",{

/***/ "(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx":
/*!*******************************************************!*\
  !*** ./components/product/RecentlyViewedProducts.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentlyViewedProducts: () => (/* binding */ RecentlyViewedProducts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProductCard */ \"(app-pages-browser)/./components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RecentlyViewedProductsComponent = (param)=>{\n    let { limit = 5, showTitle = true, className = \"\" } = param;\n    _s();\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_5__.MAIN_URL || '');\n    const { status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Use refs to track request state and prevent multiple calls\n    const isRequestInProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const hasDataBeenFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const lastFetchedStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    const fetchRecentlyViewed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RecentlyViewedProductsComponent.useCallback[fetchRecentlyViewed]\": async ()=>{\n            console.log('🔄 fetchRecentlyViewed called with status:', status);\n            console.log('🔄 isRequestInProgress:', isRequestInProgress.current);\n            console.log('🔄 hasDataBeenFetched:', hasDataBeenFetched.current);\n            console.log('🔄 lastFetchedStatus:', lastFetchedStatus.current);\n            // Prevent multiple simultaneous requests\n            if (isRequestInProgress.current) {\n                console.log('⏸️ Request already in progress, skipping...');\n                return;\n            }\n            // Skip if we already have data for this authentication status\n            if (hasDataBeenFetched.current && lastFetchedStatus.current === status) {\n                console.log('✅ Data already fetched for this status, skipping...');\n                return;\n            }\n            // Only fetch when status is determined (not 'loading')\n            if (status === 'authenticated') {\n                isRequestInProgress.current = true;\n                setLoading(true);\n                try {\n                    console.log('🚀 Fetching recently viewed products...');\n                    const response = await read('/api/v1/users/recently-viewed/');\n                    const data = Array.isArray(response) ? response : [];\n                    setRecentlyViewed(data);\n                    hasDataBeenFetched.current = true;\n                    lastFetchedStatus.current = status;\n                    console.log('✅ Successfully fetched recently viewed products:', data.length);\n                } catch (error) {\n                    console.error('❌ Error fetching recently viewed:', error);\n                    setRecentlyViewed([]);\n                } finally{\n                    setLoading(false);\n                    isRequestInProgress.current = false;\n                }\n            } else if (status === 'unauthenticated') {\n                // Only clear data when explicitly unauthenticated\n                console.log('🧹 Clearing data for unauthenticated user');\n                setRecentlyViewed([]);\n                setLoading(false);\n                hasDataBeenFetched.current = true;\n                lastFetchedStatus.current = status;\n            }\n        // Do nothing when status is 'loading' to prevent unnecessary calls\n        }\n    }[\"RecentlyViewedProductsComponent.useCallback[fetchRecentlyViewed]\"], [\n        status,\n        read\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProductsComponent.useEffect\": ()=>{\n            fetchRecentlyViewed();\n        }\n    }[\"RecentlyViewedProductsComponent.useEffect\"], [\n        fetchRecentlyViewed\n    ]);\n    // Reset fetch state when component unmounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RecentlyViewedProductsComponent.useEffect\": ()=>{\n            return ({\n                \"RecentlyViewedProductsComponent.useEffect\": ()=>{\n                    isRequestInProgress.current = false;\n                    hasDataBeenFetched.current = false;\n                    lastFetchedStatus.current = '';\n                }\n            })[\"RecentlyViewedProductsComponent.useEffect\"];\n        }\n    }[\"RecentlyViewedProductsComponent.useEffect\"], []);\n    if (status !== 'authenticated' || !loading && !(recentlyViewed === null || recentlyViewed === void 0 ? void 0 : recentlyViewed.length)) {\n        return null;\n    }\n    const displayProducts = recentlyViewed.slice(0, limit);\n    // Transform products to match ProductType interface\n    const transformedProducts = displayProducts.map((product)=>{\n        var _product_images_, _product_images, _product_category, _product_brand;\n        return {\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            rating: 0,\n            image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.image) || '/placeholder.jpg',\n            category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name) || '',\n            brand: ((_product_brand = product.brand) === null || _product_brand === void 0 ? void 0 : _product_brand.name) || '',\n            slug: product.slug\n        };\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"recently-viewed-products \".concat(className),\n            children: [\n                showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Recently Viewed\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                    children: Array.from({\n                        length: limit\n                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-48 rounded-lg mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-300 h-4 rounded w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"recently-viewed-products \".concat(className),\n        children: [\n            showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Recently Viewed\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4\",\n                children: transformedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        ...product\n                    }, product.id, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\product\\\\RecentlyViewedProducts.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecentlyViewedProductsComponent, \"ReObhpFpm4KfwjZjMiv39tbGGl0=\", false, function() {\n    return [\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = RecentlyViewedProductsComponent;\n// Memoize the component to prevent unnecessary re-renders\nconst RecentlyViewedProducts = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(RecentlyViewedProductsComponent);\n_c1 = RecentlyViewedProducts;\nvar _c, _c1;\n$RefreshReg$(_c, \"RecentlyViewedProductsComponent\");\n$RefreshReg$(_c1, \"RecentlyViewedProducts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcHJvZHVjdC9SZWNlbnRseVZpZXdlZFByb2R1Y3RzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUF3RTtBQUMzQjtBQUNMO0FBRUE7QUFDTztBQVEvQyxNQUFNUyxrQ0FBeUU7UUFBQyxFQUM5RUMsUUFBUSxDQUFDLEVBQ1RDLFlBQVksSUFBSSxFQUNoQkMsWUFBWSxFQUFFLEVBQ2Y7O0lBQ0MsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHWiwrQ0FBUUEsQ0FBUSxFQUFFO0lBQzlELE1BQU0sQ0FBQ2EsU0FBU0MsV0FBVyxHQUFHZCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLEVBQUVlLElBQUksRUFBRSxHQUFHVix5REFBTUEsQ0FBQ0Msb0RBQVFBLElBQUk7SUFDcEMsTUFBTSxFQUFFVSxNQUFNLEVBQUUsR0FBR2IsMkRBQVVBO0lBRTdCLDZEQUE2RDtJQUM3RCxNQUFNYyxzQkFBc0JoQiw2Q0FBTUEsQ0FBQztJQUNuQyxNQUFNaUIscUJBQXFCakIsNkNBQU1BLENBQUM7SUFDbEMsTUFBTWtCLG9CQUFvQmxCLDZDQUFNQSxDQUFTO0lBRXpDLE1BQU1tQixzQkFBc0JsQixrREFBV0E7NEVBQUM7WUFDdENtQixRQUFRQyxHQUFHLENBQUMsOENBQThDTjtZQUMxREssUUFBUUMsR0FBRyxDQUFDLDJCQUEyQkwsb0JBQW9CTSxPQUFPO1lBQ2xFRixRQUFRQyxHQUFHLENBQUMsMEJBQTBCSixtQkFBbUJLLE9BQU87WUFDaEVGLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJILGtCQUFrQkksT0FBTztZQUU5RCx5Q0FBeUM7WUFDekMsSUFBSU4sb0JBQW9CTSxPQUFPLEVBQUU7Z0JBQy9CRixRQUFRQyxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLDhEQUE4RDtZQUM5RCxJQUFJSixtQkFBbUJLLE9BQU8sSUFBSUosa0JBQWtCSSxPQUFPLEtBQUtQLFFBQVE7Z0JBQ3RFSyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1o7WUFDRjtZQUVBLHVEQUF1RDtZQUN2RCxJQUFJTixXQUFXLGlCQUFpQjtnQkFDOUJDLG9CQUFvQk0sT0FBTyxHQUFHO2dCQUM5QlQsV0FBVztnQkFFWCxJQUFJO29CQUNGTyxRQUFRQyxHQUFHLENBQUM7b0JBQ1osTUFBTUUsV0FBVyxNQUFNVCxLQUFLO29CQUM1QixNQUFNVSxPQUFPQyxNQUFNQyxPQUFPLENBQUNILFlBQVlBLFdBQVcsRUFBRTtvQkFDcERaLGtCQUFrQmE7b0JBQ2xCUCxtQkFBbUJLLE9BQU8sR0FBRztvQkFDN0JKLGtCQUFrQkksT0FBTyxHQUFHUDtvQkFDNUJLLFFBQVFDLEdBQUcsQ0FBQyxvREFBb0RHLEtBQUtHLE1BQU07Z0JBQzdFLEVBQUUsT0FBT0MsT0FBTztvQkFDZFIsUUFBUVEsS0FBSyxDQUFDLHFDQUFxQ0E7b0JBQ25EakIsa0JBQWtCLEVBQUU7Z0JBQ3RCLFNBQVU7b0JBQ1JFLFdBQVc7b0JBQ1hHLG9CQUFvQk0sT0FBTyxHQUFHO2dCQUNoQztZQUNGLE9BQU8sSUFBSVAsV0FBVyxtQkFBbUI7Z0JBQ3ZDLGtEQUFrRDtnQkFDbERLLFFBQVFDLEdBQUcsQ0FBQztnQkFDWlYsa0JBQWtCLEVBQUU7Z0JBQ3BCRSxXQUFXO2dCQUNYSSxtQkFBbUJLLE9BQU8sR0FBRztnQkFDN0JKLGtCQUFrQkksT0FBTyxHQUFHUDtZQUM5QjtRQUNBLG1FQUFtRTtRQUNyRTsyRUFBRztRQUFDQTtRQUFRRDtLQUFLO0lBRWpCaEIsZ0RBQVNBO3FEQUFDO1lBQ1JxQjtRQUNGO29EQUFHO1FBQUNBO0tBQW9CO0lBRXhCLDRDQUE0QztJQUM1Q3JCLGdEQUFTQTtxREFBQztZQUNSOzZEQUFPO29CQUNMa0Isb0JBQW9CTSxPQUFPLEdBQUc7b0JBQzlCTCxtQkFBbUJLLE9BQU8sR0FBRztvQkFDN0JKLGtCQUFrQkksT0FBTyxHQUFHO2dCQUM5Qjs7UUFDRjtvREFBRyxFQUFFO0lBRUwsSUFBSVAsV0FBVyxtQkFBb0IsQ0FBQ0gsV0FBVyxFQUFDRiwyQkFBQUEscUNBQUFBLGVBQWdCaUIsTUFBTSxHQUFHO1FBQ3ZFLE9BQU87SUFDVDtJQUVBLE1BQU1FLGtCQUFrQm5CLGVBQWVvQixLQUFLLENBQUMsR0FBR3ZCO0lBRWhELG9EQUFvRDtJQUNwRCxNQUFNd0Isc0JBQXFDRixnQkFBZ0JHLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFLdERBLGtCQUFBQSxpQkFDR0EsbUJBQ0hBO2VBUGtFO1lBQ3pFQyxJQUFJRCxRQUFRQyxFQUFFO1lBQ2RDLE1BQU1GLFFBQVFFLElBQUk7WUFDbEJDLE9BQU9ILFFBQVFHLEtBQUs7WUFDcEJDLFFBQVE7WUFDUkMsT0FBT0wsRUFBQUEsa0JBQUFBLFFBQVFNLE1BQU0sY0FBZE4sdUNBQUFBLG1CQUFBQSxlQUFnQixDQUFDLEVBQUUsY0FBbkJBLHVDQUFBQSxpQkFBcUJLLEtBQUssS0FBSTtZQUNyQ0UsVUFBVVAsRUFBQUEsb0JBQUFBLFFBQVFPLFFBQVEsY0FBaEJQLHdDQUFBQSxrQkFBa0JFLElBQUksS0FBSTtZQUNwQ00sT0FBT1IsRUFBQUEsaUJBQUFBLFFBQVFRLEtBQUssY0FBYlIscUNBQUFBLGVBQWVFLElBQUksS0FBSTtZQUM5Qk8sTUFBTVQsUUFBUVMsSUFBSTtRQUNwQjs7SUFFQSxJQUFJOUIsU0FBUztRQUNYLHFCQUNFLDhEQUFDK0I7WUFBSWxDLFdBQVcsNEJBQXNDLE9BQVZBOztnQkFDekNELDJCQUNDLDhEQUFDb0M7b0JBQUduQyxXQUFVOzhCQUE2Qjs7Ozs7OzhCQUU3Qyw4REFBQ2tDO29CQUFJbEMsV0FBVTs4QkFDWmdCLE1BQU1vQixJQUFJLENBQUM7d0JBQUVsQixRQUFRcEI7b0JBQU0sR0FBR3lCLEdBQUcsQ0FBQyxDQUFDYyxHQUFHQyxzQkFDckMsOERBQUNKOzRCQUFnQmxDLFdBQVU7OzhDQUN6Qiw4REFBQ2tDO29DQUFJbEMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDa0M7b0NBQUlsQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNrQztvQ0FBSWxDLFdBQVU7Ozs7Ozs7MkJBSFBzQzs7Ozs7Ozs7Ozs7Ozs7OztJQVNwQjtJQUVBLHFCQUNFLDhEQUFDSjtRQUFJbEMsV0FBVyw0QkFBc0MsT0FBVkE7O1lBQ3pDRCwyQkFDQyw4REFBQ29DO2dCQUFHbkMsV0FBVTswQkFBNkI7Ozs7OzswQkFFN0MsOERBQUNrQztnQkFBSWxDLFdBQVU7MEJBQ1pzQixvQkFBb0JDLEdBQUcsQ0FBQyxDQUFDQyx3QkFDeEIsOERBQUM5QixvREFBV0E7d0JBQW1CLEdBQUc4QixPQUFPO3VCQUF2QkEsUUFBUUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztBQUt0QztHQTlITTVCOztRQU9hRixxREFBTUE7UUFDSkYsdURBQVVBOzs7S0FSekJJO0FBZ0lOLDBEQUEwRDtBQUNuRCxNQUFNMEMsdUNBQXlCbkQsaURBQVUsQ0FBQ1MsaUNBQWlDIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxjb21wb25lbnRzXFxwcm9kdWN0XFxSZWNlbnRseVZpZXdlZFByb2R1Y3RzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuaW1wb3J0IFByb2R1Y3RDYXJkIGZyb20gJy4vUHJvZHVjdENhcmQnO1xuaW1wb3J0IHsgUHJvZHVjdFR5cGUgfSBmcm9tICcuLi8uLi90eXBlcy9wcm9kdWN0LmQnO1xuaW1wb3J0IHVzZUFwaSBmcm9tICcuLi8uLi9ob29rcy91c2VBcGknO1xuaW1wb3J0IHsgTUFJTl9VUkwgfSBmcm9tICcuLi8uLi9jb25zdGFudC91cmxzJztcblxuaW50ZXJmYWNlIFJlY2VudGx5Vmlld2VkUHJvZHVjdHNQcm9wcyB7XG4gIGxpbWl0PzogbnVtYmVyO1xuICBzaG93VGl0bGU/OiBib29sZWFuO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IFJlY2VudGx5Vmlld2VkUHJvZHVjdHNDb21wb25lbnQ6IFJlYWN0LkZDPFJlY2VudGx5Vmlld2VkUHJvZHVjdHNQcm9wcz4gPSAoe1xuICBsaW1pdCA9IDUsXG4gIHNob3dUaXRsZSA9IHRydWUsXG4gIGNsYXNzTmFtZSA9IFwiXCJcbn0pID0+IHtcbiAgY29uc3QgW3JlY2VudGx5Vmlld2VkLCBzZXRSZWNlbnRseVZpZXdlZF0gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgeyByZWFkIH0gPSB1c2VBcGkoTUFJTl9VUkwgfHwgJycpO1xuICBjb25zdCB7IHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpO1xuXG4gIC8vIFVzZSByZWZzIHRvIHRyYWNrIHJlcXVlc3Qgc3RhdGUgYW5kIHByZXZlbnQgbXVsdGlwbGUgY2FsbHNcbiAgY29uc3QgaXNSZXF1ZXN0SW5Qcm9ncmVzcyA9IHVzZVJlZihmYWxzZSk7XG4gIGNvbnN0IGhhc0RhdGFCZWVuRmV0Y2hlZCA9IHVzZVJlZihmYWxzZSk7XG4gIGNvbnN0IGxhc3RGZXRjaGVkU3RhdHVzID0gdXNlUmVmPHN0cmluZz4oJycpO1xuXG4gIGNvbnN0IGZldGNoUmVjZW50bHlWaWV3ZWQgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflIQgZmV0Y2hSZWNlbnRseVZpZXdlZCBjYWxsZWQgd2l0aCBzdGF0dXM6Jywgc3RhdHVzKTtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBpc1JlcXVlc3RJblByb2dyZXNzOicsIGlzUmVxdWVzdEluUHJvZ3Jlc3MuY3VycmVudCk7XG4gICAgY29uc29sZS5sb2coJ/CflIQgaGFzRGF0YUJlZW5GZXRjaGVkOicsIGhhc0RhdGFCZWVuRmV0Y2hlZC5jdXJyZW50KTtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBsYXN0RmV0Y2hlZFN0YXR1czonLCBsYXN0RmV0Y2hlZFN0YXR1cy5jdXJyZW50KTtcblxuICAgIC8vIFByZXZlbnQgbXVsdGlwbGUgc2ltdWx0YW5lb3VzIHJlcXVlc3RzXG4gICAgaWYgKGlzUmVxdWVzdEluUHJvZ3Jlc3MuY3VycmVudCkge1xuICAgICAgY29uc29sZS5sb2coJ+KPuO+4jyBSZXF1ZXN0IGFscmVhZHkgaW4gcHJvZ3Jlc3MsIHNraXBwaW5nLi4uJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gU2tpcCBpZiB3ZSBhbHJlYWR5IGhhdmUgZGF0YSBmb3IgdGhpcyBhdXRoZW50aWNhdGlvbiBzdGF0dXNcbiAgICBpZiAoaGFzRGF0YUJlZW5GZXRjaGVkLmN1cnJlbnQgJiYgbGFzdEZldGNoZWRTdGF0dXMuY3VycmVudCA9PT0gc3RhdHVzKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIERhdGEgYWxyZWFkeSBmZXRjaGVkIGZvciB0aGlzIHN0YXR1cywgc2tpcHBpbmcuLi4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBPbmx5IGZldGNoIHdoZW4gc3RhdHVzIGlzIGRldGVybWluZWQgKG5vdCAnbG9hZGluZycpXG4gICAgaWYgKHN0YXR1cyA9PT0gJ2F1dGhlbnRpY2F0ZWQnKSB7XG4gICAgICBpc1JlcXVlc3RJblByb2dyZXNzLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CfmoAgRmV0Y2hpbmcgcmVjZW50bHkgdmlld2VkIHByb2R1Y3RzLi4uJyk7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVhZCgnL2FwaS92MS91c2Vycy9yZWNlbnRseS12aWV3ZWQvJyk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBBcnJheS5pc0FycmF5KHJlc3BvbnNlKSA/IHJlc3BvbnNlIDogW107XG4gICAgICAgIHNldFJlY2VudGx5Vmlld2VkKGRhdGEpO1xuICAgICAgICBoYXNEYXRhQmVlbkZldGNoZWQuY3VycmVudCA9IHRydWU7XG4gICAgICAgIGxhc3RGZXRjaGVkU3RhdHVzLmN1cnJlbnQgPSBzdGF0dXM7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU3VjY2Vzc2Z1bGx5IGZldGNoZWQgcmVjZW50bHkgdmlld2VkIHByb2R1Y3RzOicsIGRhdGEubGVuZ3RoKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBmZXRjaGluZyByZWNlbnRseSB2aWV3ZWQ6JywgZXJyb3IpO1xuICAgICAgICBzZXRSZWNlbnRseVZpZXdlZChbXSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgaXNSZXF1ZXN0SW5Qcm9ncmVzcy5jdXJyZW50ID0gZmFsc2U7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChzdGF0dXMgPT09ICd1bmF1dGhlbnRpY2F0ZWQnKSB7XG4gICAgICAvLyBPbmx5IGNsZWFyIGRhdGEgd2hlbiBleHBsaWNpdGx5IHVuYXV0aGVudGljYXRlZFxuICAgICAgY29uc29sZS5sb2coJ/Cfp7kgQ2xlYXJpbmcgZGF0YSBmb3IgdW5hdXRoZW50aWNhdGVkIHVzZXInKTtcbiAgICAgIHNldFJlY2VudGx5Vmlld2VkKFtdKTtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgaGFzRGF0YUJlZW5GZXRjaGVkLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgbGFzdEZldGNoZWRTdGF0dXMuY3VycmVudCA9IHN0YXR1cztcbiAgICB9XG4gICAgLy8gRG8gbm90aGluZyB3aGVuIHN0YXR1cyBpcyAnbG9hZGluZycgdG8gcHJldmVudCB1bm5lY2Vzc2FyeSBjYWxsc1xuICB9LCBbc3RhdHVzLCByZWFkXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFJlY2VudGx5Vmlld2VkKCk7XG4gIH0sIFtmZXRjaFJlY2VudGx5Vmlld2VkXSk7XG5cbiAgLy8gUmVzZXQgZmV0Y2ggc3RhdGUgd2hlbiBjb21wb25lbnQgdW5tb3VudHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaXNSZXF1ZXN0SW5Qcm9ncmVzcy5jdXJyZW50ID0gZmFsc2U7XG4gICAgICBoYXNEYXRhQmVlbkZldGNoZWQuY3VycmVudCA9IGZhbHNlO1xuICAgICAgbGFzdEZldGNoZWRTdGF0dXMuY3VycmVudCA9ICcnO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICBpZiAoc3RhdHVzICE9PSAnYXV0aGVudGljYXRlZCcgfHwgKCFsb2FkaW5nICYmICFyZWNlbnRseVZpZXdlZD8ubGVuZ3RoKSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgY29uc3QgZGlzcGxheVByb2R1Y3RzID0gcmVjZW50bHlWaWV3ZWQuc2xpY2UoMCwgbGltaXQpO1xuXG4gIC8vIFRyYW5zZm9ybSBwcm9kdWN0cyB0byBtYXRjaCBQcm9kdWN0VHlwZSBpbnRlcmZhY2VcbiAgY29uc3QgdHJhbnNmb3JtZWRQcm9kdWN0czogUHJvZHVjdFR5cGVbXSA9IGRpc3BsYXlQcm9kdWN0cy5tYXAocHJvZHVjdCA9PiAoe1xuICAgIGlkOiBwcm9kdWN0LmlkLFxuICAgIG5hbWU6IHByb2R1Y3QubmFtZSxcbiAgICBwcmljZTogcHJvZHVjdC5wcmljZSxcbiAgICByYXRpbmc6IDAsIC8vIERlZmF1bHQgcmF0aW5nIHNpbmNlIGl0J3Mgbm90IGluIG91ciBBUEkgcmVzcG9uc2VcbiAgICBpbWFnZTogcHJvZHVjdC5pbWFnZXM/LlswXT8uaW1hZ2UgfHwgJy9wbGFjZWhvbGRlci5qcGcnLFxuICAgIGNhdGVnb3J5OiBwcm9kdWN0LmNhdGVnb3J5Py5uYW1lIHx8ICcnLFxuICAgIGJyYW5kOiBwcm9kdWN0LmJyYW5kPy5uYW1lIHx8ICcnLFxuICAgIHNsdWc6IHByb2R1Y3Quc2x1Z1xuICB9KSk7XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2ByZWNlbnRseS12aWV3ZWQtcHJvZHVjdHMgJHtjbGFzc05hbWV9YH0+XG4gICAgICAgIHtzaG93VGl0bGUgJiYgKFxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlJlY2VudGx5IFZpZXdlZDwvaDM+XG4gICAgICAgICl9XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNSBnYXAtNFwiPlxuICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiBsaW1pdCB9KS5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTMwMCBoLTQ4IHJvdW5kZWQtbGcgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMzAwIGgtNCByb3VuZGVkIG1iLTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTMwMCBoLTQgcm91bmRlZCB3LTMvNFwiPjwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2ByZWNlbnRseS12aWV3ZWQtcHJvZHVjdHMgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7c2hvd1RpdGxlICYmIChcbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+UmVjZW50bHkgVmlld2VkPC9oMz5cbiAgICAgICl9XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTMgbGc6Z3JpZC1jb2xzLTUgZ2FwLTRcIj5cbiAgICAgICAge3RyYW5zZm9ybWVkUHJvZHVjdHMubWFwKChwcm9kdWN0KSA9PiAoXG4gICAgICAgICAgPFByb2R1Y3RDYXJkIGtleT17cHJvZHVjdC5pZH0gey4uLnByb2R1Y3R9IC8+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG4vLyBNZW1vaXplIHRoZSBjb21wb25lbnQgdG8gcHJldmVudCB1bm5lY2Vzc2FyeSByZS1yZW5kZXJzXG5leHBvcnQgY29uc3QgUmVjZW50bHlWaWV3ZWRQcm9kdWN0cyA9IFJlYWN0Lm1lbW8oUmVjZW50bHlWaWV3ZWRQcm9kdWN0c0NvbXBvbmVudCk7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwidXNlU2Vzc2lvbiIsIlByb2R1Y3RDYXJkIiwidXNlQXBpIiwiTUFJTl9VUkwiLCJSZWNlbnRseVZpZXdlZFByb2R1Y3RzQ29tcG9uZW50IiwibGltaXQiLCJzaG93VGl0bGUiLCJjbGFzc05hbWUiLCJyZWNlbnRseVZpZXdlZCIsInNldFJlY2VudGx5Vmlld2VkIiwibG9hZGluZyIsInNldExvYWRpbmciLCJyZWFkIiwic3RhdHVzIiwiaXNSZXF1ZXN0SW5Qcm9ncmVzcyIsImhhc0RhdGFCZWVuRmV0Y2hlZCIsImxhc3RGZXRjaGVkU3RhdHVzIiwiZmV0Y2hSZWNlbnRseVZpZXdlZCIsImNvbnNvbGUiLCJsb2ciLCJjdXJyZW50IiwicmVzcG9uc2UiLCJkYXRhIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwiZXJyb3IiLCJkaXNwbGF5UHJvZHVjdHMiLCJzbGljZSIsInRyYW5zZm9ybWVkUHJvZHVjdHMiLCJtYXAiLCJwcm9kdWN0IiwiaWQiLCJuYW1lIiwicHJpY2UiLCJyYXRpbmciLCJpbWFnZSIsImltYWdlcyIsImNhdGVnb3J5IiwiYnJhbmQiLCJzbHVnIiwiZGl2IiwiaDMiLCJmcm9tIiwiXyIsImluZGV4IiwiUmVjZW50bHlWaWV3ZWRQcm9kdWN0cyIsIm1lbW8iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/product/RecentlyViewedProducts.tsx\n"));

/***/ })

});